import React from 'react'
import { Star } from 'lucide-react'

const Results = () => {
  const companies = [
    { name: "TechCorp", logo: "🚀" },
    { name: "StartupX", logo: "💡" },
    { name: "<PERSON><PERSON>", logo: "🎯" },
    { name: "CompanyZ", logo: "⭐" },
    { name: "InnovateA", logo: "🔥" },
    { name: "GrowthB", logo: "📈" }
  ]

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Marketing Head at TechCorp",
      content: "MetaMedia transformed our social presence. Our engagement increased by 300% in just 3 months!",
      rating: 5,
      avatar: "👩‍💼"
    },
    {
      name: "<PERSON>",
      role: "Content Creator",
      content: "The ecosystem platform helped me connect with amazing brands and grow my audience significantly.",
      rating: 5,
      avatar: "👨‍🎨"
    },
    {
      name: "<PERSON>",
      role: "Startup Founder",
      content: "Their strategic approach to content creation and paid ads delivered results beyond our expectations.",
      rating: 5,
      avatar: "👩‍💻"
    }
  ]

  const stats = [
    { number: "500+", label: "Successful Campaigns" },
    { number: "200+", label: "Happy Clients" },
    { number: "1M+", label: "Content Views" },
    { number: "95%", label: "Client Retention" }
  ]

  return (
    <section className="section" id="results" style={{background: 'var(--gray-50)'}}>
      <div className="container">
        <div className="text-center" style={{marginBottom: '4rem'}}>
          <h2 style={{marginBottom: '1rem'}}>Results That Speak</h2>
          <p style={{fontSize: '1.125rem', color: 'var(--gray-600)'}}>Trusted by leading brands and creators worldwide</p>
        </div>

        {/* Company Logos */}
        <div style={{marginBottom: '4rem'}}>
          <h3 className="text-center" style={{marginBottom: '2rem', color: 'var(--gray-700)'}}>Companies We've Worked With</h3>
          <div className="grid" style={{gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '2rem', alignItems: 'center'}}>
            {companies.map((company, index) => (
              <div key={index} style={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '0.5rem', padding: '1rem', background: 'var(--white)', borderRadius: '0.5rem', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)', transition: 'transform 0.3s ease'}}>
                <span style={{fontSize: '2rem'}}>{company.logo}</span>
                <span style={{fontWeight: 600, color: 'var(--gray-700)'}}>{company.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div style={{marginBottom: '4rem'}}>
          <div style={{background: 'var(--white)', borderRadius: '1rem', padding: '3rem 2rem', boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)', display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '2rem'}}>
            {stats.map((stat, index) => (
              <div key={index} style={{textAlign: 'center'}}>
                <div style={{fontSize: '3rem', fontWeight: 800, color: 'var(--primary-blue)', marginBottom: '0.5rem'}}>{stat.number}</div>
                <div style={{fontWeight: 600, color: 'var(--gray-600)'}}>{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div>
          <h3 className="text-center" style={{marginBottom: '3rem', color: 'var(--gray-900)'}}>What Our Clients Say</h3>
          <div className="grid grid-3">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="card" style={{background: 'var(--white)', position: 'relative'}}>
                <div style={{display: 'flex', gap: '0.25rem', marginBottom: '1rem', color: '#fbbf24'}}>
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <p style={{fontStyle: 'italic', marginBottom: '1.5rem', color: 'var(--gray-700)', lineHeight: 1.6}}>"{testimonial.content}"</p>
                <div style={{display: 'flex', alignItems: 'center', gap: '1rem'}}>
                  <span style={{fontSize: '2rem'}}>{testimonial.avatar}</span>
                  <div>
                    <div style={{fontWeight: 600, color: 'var(--gray-900)'}}>{testimonial.name}</div>
                    <div style={{fontSize: '0.875rem', color: 'var(--gray-500)'}}>{testimonial.role}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        .results {
          background: var(--gray-50);
        }

        .results-header {
          margin-bottom: 4rem;
        }

        .results-header h2 {
          margin-bottom: 1rem;
        }

        .results-header p {
          font-size: 1.125rem;
          color: var(--gray-600);
        }

        .companies-section {
          margin-bottom: 4rem;
        }

        .companies-section h3 {
          margin-bottom: 2rem;
          color: var(--gray-700);
        }

        .companies-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 2rem;
          align-items: center;
        }

        .company-logo {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          padding: 1rem;
          background: var(--white);
          border-radius: 0.5rem;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
          transition: transform 0.3s ease;
        }

        .company-logo:hover {
          transform: translateY(-2px);
        }

        .logo-emoji {
          font-size: 2rem;
        }

        .company-name {
          font-weight: 600;
          color: var(--gray-700);
        }

        .stats-section {
          margin-bottom: 4rem;
        }

        .stats-grid {
          background: var(--white);
          border-radius: 1rem;
          padding: 3rem 2rem;
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stat-item {
          text-align: center;
        }

        .stat-number {
          font-size: 3rem;
          font-weight: 800;
          color: var(--primary-blue);
          margin-bottom: 0.5rem;
        }

        .stat-label {
          font-weight: 600;
          color: var(--gray-600);
        }

        .testimonials-section h3 {
          margin-bottom: 3rem;
          color: var(--gray-900);
        }

        .testimonials-grid {
          gap: 2rem;
        }

        .testimonial-card {
          background: var(--white);
          position: relative;
        }

        .testimonial-rating {
          display: flex;
          gap: 0.25rem;
          margin-bottom: 1rem;
          color: #fbbf24;
        }

        .testimonial-content {
          font-style: italic;
          margin-bottom: 1.5rem;
          color: var(--gray-700);
          line-height: 1.6;
        }

        .testimonial-author {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .author-avatar {
          font-size: 2rem;
        }

        .author-name {
          font-weight: 600;
          color: var(--gray-900);
        }

        .author-role {
          font-size: 0.875rem;
          color: var(--gray-500);
        }

        @media (max-width: 768px) {
          .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
          }

          .testimonials-grid {
            grid-template-columns: 1fr;
          }

          .companies-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }
      `}</style>
    </section>
  )
}

export default Results
