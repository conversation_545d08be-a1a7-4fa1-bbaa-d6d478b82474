import React, { useState } from 'react'
import { Menu, X } from 'lucide-react'
import './Header.css'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <div className="logo-icon">
              <div className="logo-circle"></div>
              <div className="logo-text">META MEDIA</div>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="nav-desktop">
            <a href="#home" className="nav-link">Home</a>
            <a href="#services" className="nav-link">Services</a>
            <a href="#ecosystem" className="nav-link">Ecosystem</a>
            <a href="#blog" className="nav-link">Blog</a>
            <a href="#contact" className="nav-link">Contact</a>
          </nav>

          {/* CTA Button */}
          <div className="header-cta">
            <a href="#login" className="btn btn-primary">Login</a>
          </div>

          {/* Mobile Menu Button */}
          <button 
            className="mobile-menu-btn"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="nav-mobile">
            <a href="#home" className="nav-link-mobile">Home</a>
            <a href="#services" className="nav-link-mobile">Services</a>
            <a href="#ecosystem" className="nav-link-mobile">Ecosystem</a>
            <a href="#blog" className="nav-link-mobile">Blog</a>
            <a href="#contact" className="nav-link-mobile">Contact</a>
            <a href="#login" className="btn btn-primary mobile-cta">Login</a>
          </nav>
        )}
      </div>


    </header>
  )
}

export default Header
