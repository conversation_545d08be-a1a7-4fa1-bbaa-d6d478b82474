import React, { useState } from 'react'
import { Mail, Instagram, Youtube, Linkedin, Twitter, ArrowRight } from 'lucide-react'

const Footer = () => {
  const [email, setEmail] = useState('')

  const handleNewsletterSubmit = (e) => {
    e.preventDefault()
    console.log('Newsletter signup:', email)
    setEmail('')
  }

  return (
    <footer style={{background: 'var(--gray-900)', color: 'var(--white)', padding: '4rem 0 2rem'}}>
      <div className="container">
        <div style={{display: 'grid', gridTemplateColumns: '1fr 2fr 1fr', gap: '4rem', marginBottom: '3rem'}}>
          {/* Logo and Tagline */}
          <div style={{display: 'flex', flexDirection: 'column', gap: '1.5rem'}}>
            <div>
              <div style={{display: 'flex', alignItems: 'center', gap: '0.5rem'}}>
                <div style={{width: '40px', height: '40px', background: 'var(--gradient-primary)', borderRadius: '50%', position: 'relative'}}>
                  <div style={{content: '', position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', width: '20px', height: '20px', background: 'var(--white)', borderRadius: '50%'}}></div>
                </div>
                <div style={{fontSize: '1.25rem', fontWeight: 700, color: 'var(--white)'}}>META MEDIA</div>
              </div>
            </div>
            <p style={{color: 'var(--gray-300)', lineHeight: 1.6}}>
              More Than Marketing. A Movement.
            </p>
            <div style={{display: 'flex', gap: '1rem'}}>
              <a href="#instagram" style={{display: 'flex', alignItems: 'center', justifyContent: 'center', width: '40px', height: '40px', background: 'var(--gray-800)', borderRadius: '50%', color: 'var(--gray-300)', transition: 'all 0.3s ease'}}>
                <Instagram size={20} />
              </a>
              <a href="#youtube" style={{display: 'flex', alignItems: 'center', justifyContent: 'center', width: '40px', height: '40px', background: 'var(--gray-800)', borderRadius: '50%', color: 'var(--gray-300)', transition: 'all 0.3s ease'}}>
                <Youtube size={20} />
              </a>
              <a href="#linkedin" style={{display: 'flex', alignItems: 'center', justifyContent: 'center', width: '40px', height: '40px', background: 'var(--gray-800)', borderRadius: '50%', color: 'var(--gray-300)', transition: 'all 0.3s ease'}}>
                <Linkedin size={20} />
              </a>
              <a href="#twitter" style={{display: 'flex', alignItems: 'center', justifyContent: 'center', width: '40px', height: '40px', background: 'var(--gray-800)', borderRadius: '50%', color: 'var(--gray-300)', transition: 'all 0.3s ease'}}>
                <Twitter size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div style={{display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: '2rem'}}>
            <div>
              <h4 style={{color: 'var(--white)', marginBottom: '1rem', fontWeight: 600}}>Services</h4>
              <ul style={{listStyle: 'none', padding: 0, margin: 0}}>
                <li style={{marginBottom: '0.75rem'}}><a href="#content-creation" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Content Creation</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#social-strategy" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Social Strategy</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#paid-ads" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Paid Advertising</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#analytics" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Analytics & Insights</a></li>
              </ul>
            </div>

            <div>
              <h4 style={{color: 'var(--white)', marginBottom: '1rem', fontWeight: 600}}>Ecosystem</h4>
              <ul style={{listStyle: 'none', padding: 0, margin: 0}}>
                <li style={{marginBottom: '0.75rem'}}><a href="#join-creator" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Join as Creator</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#list-brand" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>List Your Brand</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#how-it-works" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>How It Works</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#success-stories" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Success Stories</a></li>
              </ul>
            </div>

            <div>
              <h4 style={{color: 'var(--white)', marginBottom: '1rem', fontWeight: 600}}>Resources</h4>
              <ul style={{listStyle: 'none', padding: 0, margin: 0}}>
                <li style={{marginBottom: '0.75rem'}}><a href="#blog" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Blog</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#podcast" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Podcast</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#case-studies" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Case Studies</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#guides" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Guides</a></li>
              </ul>
            </div>

            <div>
              <h4 style={{color: 'var(--white)', marginBottom: '1rem', fontWeight: 600}}>Company</h4>
              <ul style={{listStyle: 'none', padding: 0, margin: 0}}>
                <li style={{marginBottom: '0.75rem'}}><a href="#about" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>About Us</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#contact" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Contact</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#careers" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Careers</a></li>
                <li style={{marginBottom: '0.75rem'}}><a href="#privacy" style={{color: 'var(--gray-300)', transition: 'color 0.3s ease'}}>Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          {/* Newsletter Signup */}
          <div style={{display: 'flex', flexDirection: 'column', gap: '1rem'}}>
            <h4 style={{color: 'var(--white)', fontWeight: 600}}>Stay Updated</h4>
            <p style={{color: 'var(--gray-300)', lineHeight: 1.6}}>Get the latest insights and trends delivered to your inbox.</p>
            <form onSubmit={handleNewsletterSubmit} style={{marginTop: '1rem'}}>
              <div style={{position: 'relative', display: 'flex', alignItems: 'center'}}>
                <Mail size={20} style={{position: 'absolute', left: '1rem', color: 'var(--gray-400)', zIndex: 1}} />
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  style={{width: '100%', padding: '0.75rem 1rem 0.75rem 3rem', background: 'var(--gray-800)', border: '1px solid var(--gray-700)', borderRadius: '0.5rem', color: 'var(--white)', fontSize: '0.875rem'}}
                />
                <button type="submit" style={{position: 'absolute', right: '0.5rem', background: 'var(--primary-blue)', border: 'none', borderRadius: '0.25rem', padding: '0.5rem', color: 'var(--white)', cursor: 'pointer', transition: 'background 0.3s ease'}}>
                  <ArrowRight size={20} />
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Footer Bottom */}
        <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', paddingTop: '2rem', borderTop: '1px solid var(--gray-800)', color: 'var(--gray-400)', fontSize: '0.875rem'}}>
          <p>&copy; 2023 MetaMedia. All rights reserved.</p>
          <div style={{display: 'flex', gap: '2rem'}}>
            <a href="#terms" style={{color: 'var(--gray-400)', transition: 'color 0.3s ease'}}>Terms of Service</a>
            <a href="#privacy" style={{color: 'var(--gray-400)', transition: 'color 0.3s ease'}}>Privacy Policy</a>
            <a href="#cookies" style={{color: 'var(--gray-400)', transition: 'color 0.3s ease'}}>Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
