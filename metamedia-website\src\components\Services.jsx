import React from 'react'
import { <PERSON>, Target, PenTool, ArrowRight } from 'lucide-react'
import './Components.css'

const Services = () => {
  const services = [
    {
      icon: <Video size={40} />,
      title: "Content Creation",
      description: "Instagram Reels, YouTube Shorts, and viral content that converts.",
      features: ["Reels & Shorts Creation", "YouTube Scripts", "Social Media Captions"]
    },
    {
      icon: <Target size={40} />,
      title: "Social Media Strategy",
      description: "Monthly calendars and community management that builds engagement.",
      features: ["Monthly Calendars", "Community Management", "Content Planning"]
    },
    {
      icon: <PenTool size={40} />,
      title: "Paid Ads + Analytics",
      description: "Performance marketing campaigns with detailed insights and optimization.",
      features: ["Meta & Google Ads", "Analytics Reports", "A/B Testing"]
    }
  ]

  return (
    <section className="services section" id="services">
      <div className="container">
        <div className="services-header text-center">
          <h2>What We Do</h2>
          <p>Comprehensive digital marketing services to grow your brand</p>
        </div>

        <div className="services-grid grid grid-3">
          {services.map((service, index) => (
            <div key={index} className="service-card card">
              <div className="service-icon">
                {service.icon}
              </div>
              <h3>{service.title}</h3>
              <p>{service.description}</p>
              <ul className="service-features">
                {service.features.map((feature, idx) => (
                  <li key={idx}>{feature}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="services-cta text-center">
          <a href="#all-services" className="btn btn-primary">
            View All Services
            <ArrowRight size={20} />
          </a>
        </div>
      </div>


    </section>
  )
}

export default Services
