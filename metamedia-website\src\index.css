:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Meta Media Color Scheme */
  --primary-blue: #4A90E2;
  --secondary-blue: #6BB6FF;
  --accent-purple: #8B5CF6;
  --gradient-purple: #A855F7;
  --gradient-pink: #EC4899;
  --gradient-cyan: #06B6D4;
  --dark-blue: #1E3A8A;
  --light-blue: #DBEAFE;
  --white: #FFFFFF;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  --gradient-hero: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-purple) 50%, var(--gradient-pink) 100%);
  --gradient-card: linear-gradient(135deg, var(--white) 0%, var(--light-blue) 100%);
  --gradient-button: linear-gradient(135deg, var(--accent-purple) 0%, var(--gradient-purple) 100%);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--white);
  color: var(--gray-800);
  overflow-x: hidden;
}

a {
  font-weight: 500;
  color: var(--primary-blue);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--accent-purple);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--gray-900);
}

h1 {
  font-size: 3.5rem;
  line-height: 1.1;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 1.875rem;
}

p {
  line-height: 1.6;
  color: var(--gray-600);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--gradient-button);
  color: var(--white);
  box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(139, 92, 246, 0.4);
  color: var(--white);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
}

.section {
  padding: 5rem 0;
}

.section-sm {
  padding: 3rem 0;
}

.text-center {
  text-align: center;
}

.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.card {
  background: var(--white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .container {
    padding: 0 1rem;
  }

  .section {
    padding: 3rem 0;
  }

  .grid-3 {
    grid-template-columns: 1fr;
  }

  .grid-2 {
    grid-template-columns: 1fr;
  }
}
