import React, { useState } from 'react'
import { Mail, Instagram, Youtube, Linkedin, Twitter, ArrowRight } from 'lucide-react'

const Footer = () => {
  const [email, setEmail] = useState('')

  const handleNewsletterSubmit = (e) => {
    e.preventDefault()
    // Handle newsletter signup
    console.log('Newsletter signup:', email)
    setEmail('')
  }

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          {/* Logo and Tagline */}
          <div className="footer-brand">
            <div className="footer-logo">
              <div className="logo-icon">
                <div className="logo-circle"></div>
                <div className="logo-text">META MEDIA</div>
              </div>
            </div>
            <p className="footer-tagline">
              More Than Marketing. A Movement.
            </p>
            <div className="social-links">
              <a href="#instagram" className="social-link">
                <Instagram size={20} />
              </a>
              <a href="#youtube" className="social-link">
                <Youtube size={20} />
              </a>
              <a href="#linkedin" className="social-link">
                <Linkedin size={20} />
              </a>
              <a href="#twitter" className="social-link">
                <Twitter size={20} />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-links">
            <div className="link-group">
              <h4>Services</h4>
              <ul>
                <li><a href="#content-creation">Content Creation</a></li>
                <li><a href="#social-strategy">Social Strategy</a></li>
                <li><a href="#paid-ads">Paid Advertising</a></li>
                <li><a href="#analytics">Analytics & Insights</a></li>
              </ul>
            </div>

            <div className="link-group">
              <h4>Ecosystem</h4>
              <ul>
                <li><a href="#join-creator">Join as Creator</a></li>
                <li><a href="#list-brand">List Your Brand</a></li>
                <li><a href="#how-it-works">How It Works</a></li>
                <li><a href="#success-stories">Success Stories</a></li>
              </ul>
            </div>

            <div className="link-group">
              <h4>Resources</h4>
              <ul>
                <li><a href="#blog">Blog</a></li>
                <li><a href="#podcast">Podcast</a></li>
                <li><a href="#case-studies">Case Studies</a></li>
                <li><a href="#guides">Guides</a></li>
              </ul>
            </div>

            <div className="link-group">
              <h4>Company</h4>
              <ul>
                <li><a href="#about">About Us</a></li>
                <li><a href="#contact">Contact</a></li>
                <li><a href="#careers">Careers</a></li>
                <li><a href="#privacy">Privacy Policy</a></li>
              </ul>
            </div>
          </div>

          {/* Newsletter Signup */}
          <div className="newsletter">
            <h4>Stay Updated</h4>
            <p>Get the latest insights and trends delivered to your inbox.</p>
            <form onSubmit={handleNewsletterSubmit} className="newsletter-form">
              <div className="input-group">
                <Mail size={20} className="input-icon" />
                <input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <button type="submit" className="submit-btn">
                  <ArrowRight size={20} />
                </button>
              </div>
            </form>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <p>&copy; 2023 MetaMedia. All rights reserved.</p>
          <div className="footer-bottom-links">
            <a href="#terms">Terms of Service</a>
            <a href="#privacy">Privacy Policy</a>
            <a href="#cookies">Cookie Policy</a>
          </div>
        </div>
      </div>

      <style jsx>{`
        .footer {
          background: var(--gray-900);
          color: var(--white);
          padding: 4rem 0 2rem;
        }

        .footer-content {
          display: grid;
          grid-template-columns: 1fr 2fr 1fr;
          gap: 4rem;
          margin-bottom: 3rem;
        }

        .footer-brand {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .footer-logo .logo-icon {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .footer-logo .logo-circle {
          width: 40px;
          height: 40px;
          background: var(--gradient-primary);
          border-radius: 50%;
          position: relative;
        }

        .footer-logo .logo-circle::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 20px;
          height: 20px;
          background: var(--white);
          border-radius: 50%;
        }

        .footer-logo .logo-text {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--white);
        }

        .footer-tagline {
          color: var(--gray-300);
          line-height: 1.6;
        }

        .social-links {
          display: flex;
          gap: 1rem;
        }

        .social-link {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          background: var(--gray-800);
          border-radius: 50%;
          color: var(--gray-300);
          transition: all 0.3s ease;
        }

        .social-link:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: translateY(-2px);
        }

        .footer-links {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 2rem;
        }

        .link-group h4 {
          color: var(--white);
          margin-bottom: 1rem;
          font-weight: 600;
        }

        .link-group ul {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .link-group li {
          margin-bottom: 0.75rem;
        }

        .link-group a {
          color: var(--gray-300);
          transition: color 0.3s ease;
        }

        .link-group a:hover {
          color: var(--primary-blue);
        }

        .newsletter {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .newsletter h4 {
          color: var(--white);
          font-weight: 600;
        }

        .newsletter p {
          color: var(--gray-300);
          line-height: 1.6;
        }

        .newsletter-form {
          margin-top: 1rem;
        }

        .input-group {
          position: relative;
          display: flex;
          align-items: center;
        }

        .input-icon {
          position: absolute;
          left: 1rem;
          color: var(--gray-400);
          z-index: 1;
        }

        .input-group input {
          width: 100%;
          padding: 0.75rem 1rem 0.75rem 3rem;
          background: var(--gray-800);
          border: 1px solid var(--gray-700);
          border-radius: 0.5rem;
          color: var(--white);
          font-size: 0.875rem;
        }

        .input-group input::placeholder {
          color: var(--gray-400);
        }

        .input-group input:focus {
          outline: none;
          border-color: var(--primary-blue);
        }

        .submit-btn {
          position: absolute;
          right: 0.5rem;
          background: var(--primary-blue);
          border: none;
          border-radius: 0.25rem;
          padding: 0.5rem;
          color: var(--white);
          cursor: pointer;
          transition: background 0.3s ease;
        }

        .submit-btn:hover {
          background: var(--accent-purple);
        }

        .footer-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 2rem;
          border-top: 1px solid var(--gray-800);
          color: var(--gray-400);
          font-size: 0.875rem;
        }

        .footer-bottom-links {
          display: flex;
          gap: 2rem;
        }

        .footer-bottom-links a {
          color: var(--gray-400);
          transition: color 0.3s ease;
        }

        .footer-bottom-links a:hover {
          color: var(--primary-blue);
        }

        @media (max-width: 768px) {
          .footer-content {
            grid-template-columns: 1fr;
            gap: 3rem;
          }

          .footer-links {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
          }

          .footer-bottom {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
          }

          .footer-bottom-links {
            gap: 1rem;
          }
        }
      `}</style>
    </footer>
  )
}

export default Footer
