import React from 'react'
import { Calendar, ArrowRight, Play } from 'lucide-react'

const BlogTeaser = () => {
  const blogPosts = [
    {
      title: "The Future of UGC Marketing in 2024",
      excerpt: "Discover the latest trends and strategies that are shaping user-generated content marketing.",
      date: "Dec 15, 2023",
      readTime: "5 min read",
      category: "Strategy"
    },
    {
      title: "How to Build an Authentic Creator Community",
      excerpt: "Learn the essential steps to create and nurture a thriving community of content creators.",
      date: "Dec 12, 2023",
      readTime: "7 min read",
      category: "Community"
    },
    {
      title: "Maximizing ROI with Performance Marketing",
      excerpt: "Data-driven strategies to optimize your ad spend and achieve better conversion rates.",
      date: "Dec 10, 2023",
      readTime: "6 min read",
      category: "Analytics"
    }
  ]

  return (
    <section className="section" id="blog" style={{background: 'var(--white)'}}>
      <div className="container">
        <div className="text-center" style={{marginBottom: '4rem'}}>
          <h2 style={{marginBottom: '1rem'}}>Insight Hub</h2>
          <p style={{fontSize: '1.125rem', color: 'var(--gray-600)'}}>Stay ahead with the latest digital marketing insights and trends</p>
        </div>

        <div style={{marginBottom: '3rem'}}>
          <div style={{marginBottom: '4rem'}}>
            <h3 style={{marginBottom: '2rem', color: 'var(--gray-900)'}}>Latest Articles</h3>
            <div className="grid grid-3" style={{gap: '2rem'}}>
              {blogPosts.map((post, index) => (
                <article key={index} className="card" style={{transition: 'all 0.3s ease'}}>
                  <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem'}}>
                    <span style={{background: 'var(--gradient-primary)', color: 'var(--white)', padding: '0.25rem 0.75rem', borderRadius: '1rem', fontSize: '0.75rem', fontWeight: 600}}>{post.category}</span>
                    <div style={{display: 'flex', alignItems: 'center', gap: '0.5rem', color: 'var(--gray-500)', fontSize: '0.875rem'}}>
                      <Calendar size={14} />
                      {post.date}
                    </div>
                  </div>
                  <h4 style={{marginBottom: '1rem', color: 'var(--gray-900)', lineHeight: 1.4}}>{post.title}</h4>
                  <p style={{marginBottom: '1.5rem', color: 'var(--gray-600)', lineHeight: 1.6}}>{post.excerpt}</p>
                  <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                    <span style={{color: 'var(--gray-500)', fontSize: '0.875rem'}}>{post.readTime}</span>
                    <a href="#read-more" style={{display: 'flex', alignItems: 'center', gap: '0.5rem', color: 'var(--primary-blue)', fontWeight: 600, transition: 'color 0.3s ease'}}>
                      Read More <ArrowRight size={14} />
                    </a>
                  </div>
                </article>
              ))}
            </div>
          </div>

          <div>
            <div>
              <h3 style={{marginBottom: '2rem', color: 'var(--gray-900)'}}>Podcast & Video Content</h3>
              <div style={{display: 'grid', gap: '1.5rem'}}>
                <div style={{display: 'flex', gap: '1rem', padding: '1.5rem', background: 'var(--gray-50)', borderRadius: '1rem', transition: 'all 0.3s ease'}}>
                  <div style={{position: 'relative', width: '80px', height: '80px', background: 'var(--gradient-primary)', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'var(--white)'}}>
                    <Play size={24} fill="currentColor" style={{position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', opacity: 0.8}} />
                    <div style={{position: 'absolute', bottom: '-0.5rem', left: '50%', transform: 'translateX(-50%)', background: 'var(--white)', padding: '0.25rem 0.5rem', borderRadius: '0.25rem', fontSize: '0.75rem', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'}}>🎧 Podcast</div>
                  </div>
                  <div>
                    <h4 style={{marginBottom: '0.5rem', color: 'var(--gray-900)'}}>The Creator Economy Revolution</h4>
                    <p style={{color: 'var(--gray-500)', fontSize: '0.875rem'}}>Episode 15 • 45 min</p>
                  </div>
                </div>

                <div style={{display: 'flex', gap: '1rem', padding: '1.5rem', background: 'var(--gray-50)', borderRadius: '1rem', transition: 'all 0.3s ease'}}>
                  <div style={{position: 'relative', width: '80px', height: '80px', background: 'var(--gradient-primary)', borderRadius: '0.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'var(--white)'}}>
                    <Play size={24} fill="currentColor" style={{position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', opacity: 0.8}} />
                    <div style={{position: 'absolute', bottom: '-0.5rem', left: '50%', transform: 'translateX(-50%)', background: 'var(--white)', padding: '0.25rem 0.5rem', borderRadius: '0.25rem', fontSize: '0.75rem', boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)'}}>📹 YouTube</div>
                  </div>
                  <div>
                    <h4 style={{marginBottom: '0.5rem', color: 'var(--gray-900)'}}>5 TikTok Trends That Convert</h4>
                    <p style={{color: 'var(--gray-500)', fontSize: '0.875rem'}}>Short • 3 min</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center" style={{marginTop: '2rem'}}>
          <a href="#all-insights" className="btn btn-primary">
            Explore All Insights
            <ArrowRight size={20} />
          </a>
        </div>
      </div>
    </section>
  )
}

export default BlogTeaser
