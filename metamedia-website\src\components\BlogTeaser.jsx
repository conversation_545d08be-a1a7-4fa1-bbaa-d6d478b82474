import React from 'react'
import { Calendar, ArrowRight, Play } from 'lucide-react'

const BlogTeaser = () => {
  const blogPosts = [
    {
      title: "The Future of UGC Marketing in 2024",
      excerpt: "Discover the latest trends and strategies that are shaping user-generated content marketing.",
      date: "Dec 15, 2023",
      readTime: "5 min read",
      category: "Strategy"
    },
    {
      title: "How to Build an Authentic Creator Community",
      excerpt: "Learn the essential steps to create and nurture a thriving community of content creators.",
      date: "Dec 12, 2023",
      readTime: "7 min read",
      category: "Community"
    },
    {
      title: "Maximizing ROI with Performance Marketing",
      excerpt: "Data-driven strategies to optimize your ad spend and achieve better conversion rates.",
      date: "Dec 10, 2023",
      readTime: "6 min read",
      category: "Analytics"
    }
  ]

  return (
    <section className="blog-teaser section" id="blog">
      <div className="container">
        <div className="blog-header text-center">
          <h2>Insight Hub</h2>
          <p>Stay ahead with the latest digital marketing insights and trends</p>
        </div>

        <div className="blog-content">
          <div className="blog-posts">
            <h3>Latest Articles</h3>
            <div className="posts-grid grid grid-3">
              {blogPosts.map((post, index) => (
                <article key={index} className="blog-card card">
                  <div className="blog-meta">
                    <span className="blog-category">{post.category}</span>
                    <div className="blog-date">
                      <Calendar size={14} />
                      {post.date}
                    </div>
                  </div>
                  <h4 className="blog-title">{post.title}</h4>
                  <p className="blog-excerpt">{post.excerpt}</p>
                  <div className="blog-footer">
                    <span className="read-time">{post.readTime}</span>
                    <a href="#read-more" className="read-more">
                      Read More <ArrowRight size={14} />
                    </a>
                  </div>
                </article>
              ))}
            </div>
          </div>

          <div className="media-content">
            <div className="media-section">
              <h3>Podcast & Video Content</h3>
              <div className="media-grid">
                <div className="media-item podcast-item">
                  <div className="media-thumbnail">
                    <div className="play-button">
                      <Play size={24} fill="currentColor" />
                    </div>
                    <div className="media-type">🎧 Podcast</div>
                  </div>
                  <div className="media-info">
                    <h4>The Creator Economy Revolution</h4>
                    <p>Episode 15 • 45 min</p>
                  </div>
                </div>

                <div className="media-item video-item">
                  <div className="media-thumbnail">
                    <div className="play-button">
                      <Play size={24} fill="currentColor" />
                    </div>
                    <div className="media-type">📹 YouTube</div>
                  </div>
                  <div className="media-info">
                    <h4>5 TikTok Trends That Convert</h4>
                    <p>Short • 3 min</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="blog-cta text-center">
          <a href="#all-insights" className="btn btn-primary">
            Explore All Insights
            <ArrowRight size={20} />
          </a>
        </div>
      </div>

      <style jsx>{`
        .blog-teaser {
          background: var(--white);
        }

        .blog-header {
          margin-bottom: 4rem;
        }

        .blog-header h2 {
          margin-bottom: 1rem;
        }

        .blog-header p {
          font-size: 1.125rem;
          color: var(--gray-600);
        }

        .blog-content {
          margin-bottom: 3rem;
        }

        .blog-posts {
          margin-bottom: 4rem;
        }

        .blog-posts h3 {
          margin-bottom: 2rem;
          color: var(--gray-900);
        }

        .posts-grid {
          gap: 2rem;
        }

        .blog-card {
          transition: all 0.3s ease;
        }

        .blog-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .blog-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .blog-category {
          background: var(--gradient-primary);
          color: var(--white);
          padding: 0.25rem 0.75rem;
          border-radius: 1rem;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .blog-date {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--gray-500);
          font-size: 0.875rem;
        }

        .blog-title {
          margin-bottom: 1rem;
          color: var(--gray-900);
          line-height: 1.4;
        }

        .blog-excerpt {
          margin-bottom: 1.5rem;
          color: var(--gray-600);
          line-height: 1.6;
        }

        .blog-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .read-time {
          color: var(--gray-500);
          font-size: 0.875rem;
        }

        .read-more {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--primary-blue);
          font-weight: 600;
          transition: color 0.3s ease;
        }

        .read-more:hover {
          color: var(--accent-purple);
        }

        .media-section h3 {
          margin-bottom: 2rem;
          color: var(--gray-900);
        }

        .media-grid {
          display: grid;
          gap: 1.5rem;
        }

        .media-item {
          display: flex;
          gap: 1rem;
          padding: 1.5rem;
          background: var(--gray-50);
          border-radius: 1rem;
          transition: all 0.3s ease;
        }

        .media-item:hover {
          background: var(--white);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .media-thumbnail {
          position: relative;
          width: 80px;
          height: 80px;
          background: var(--gradient-primary);
          border-radius: 0.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--white);
        }

        .play-button {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          opacity: 0.8;
        }

        .media-type {
          position: absolute;
          bottom: -0.5rem;
          left: 50%;
          transform: translateX(-50%);
          background: var(--white);
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .media-info h4 {
          margin-bottom: 0.5rem;
          color: var(--gray-900);
        }

        .media-info p {
          color: var(--gray-500);
          font-size: 0.875rem;
        }

        .blog-cta {
          margin-top: 2rem;
        }

        @media (max-width: 768px) {
          .posts-grid {
            grid-template-columns: 1fr;
          }

          .media-item {
            flex-direction: column;
            text-align: center;
          }

          .media-thumbnail {
            align-self: center;
          }
        }
      `}</style>
    </section>
  )
}

export default BlogTeaser
