import React from 'react'
import { Users, Building2, RefreshCw } from 'lucide-react'

const Ecosystem = () => {
  return (
    <section className="section" id="ecosystem" style={{background: 'var(--white)'}}>
      <div className="container">
        <div className="text-center" style={{marginBottom: '4rem'}}>
          <h2>The MetaMedia Ecosystem</h2>
          <p style={{fontSize: '1.25rem', maxWidth: '600px', margin: '0 auto', color: 'var(--gray-600)'}}>
            Not just a service agency, we're building a creative economy.
          </p>
        </div>

        <div style={{marginBottom: '4rem'}}>
          <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '3rem', flexWrap: 'wrap'}}>
            <div className="card" style={{maxWidth: '350px', textAlign: 'center', border: '2px solid var(--accent-purple)'}}>
              <div style={{display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: '80px', height: '80px', background: 'var(--gradient-primary)', borderRadius: '50%', marginBottom: '1rem', color: 'var(--white)'}}>
                <Users size={40} />
              </div>
              <h3>💡 Creators</h3>
              <p>Join our platform, find briefs, pitch content, monetize your audience.</p>
              <ul style={{listStyle: 'none', padding: 0, marginBottom: '2rem', textAlign: 'left'}}>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Create profile with portfolio
                </li>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Browse brand briefs
                </li>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Submit UGC pitches
                </li>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Earn, get rated, build community
                </li>
              </ul>
              <a href="#become-creator" className="btn btn-primary">
                Become a Creator
              </a>
            </div>

            <div style={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '0.5rem', color: 'var(--primary-blue)', fontWeight: 600}}>
              <RefreshCw size={30} />
              <span>Match. Create. Grow.</span>
            </div>

            <div className="card" style={{maxWidth: '350px', textAlign: 'center', border: '2px solid var(--primary-blue)'}}>
              <div style={{display: 'inline-flex', alignItems: 'center', justifyContent: 'center', width: '80px', height: '80px', background: 'var(--gradient-primary)', borderRadius: '50%', marginBottom: '1rem', color: 'var(--white)'}}>
                <Building2 size={40} />
              </div>
              <h3>🏢 Brands</h3>
              <p>Post UGC campaigns, discover authentic creators, grow your reach.</p>
              <ul style={{listStyle: 'none', padding: 0, marginBottom: '2rem', textAlign: 'left'}}>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Post creative requirements
                </li>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Filter creators by niche & engagement
                </li>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Chat, collaborate, contract & rate
                </li>
                <li style={{padding: '0.5rem 0', color: 'var(--gray-600)', position: 'relative', paddingLeft: '1.5rem'}}>
                  <span style={{position: 'absolute', left: 0, color: 'var(--primary-blue)', fontWeight: 'bold'}}>→</span>
                  Download analytics for content
                </li>
              </ul>
              <a href="#list-brand" className="btn btn-secondary">
                List Your Brand
              </a>
            </div>
          </div>
        </div>

        <div style={{marginTop: '4rem'}}>
          <div className="grid grid-3">
            <div style={{textAlign: 'center', padding: '2rem'}}>
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🎯</div>
              <h4 style={{marginBottom: '1rem', color: 'var(--gray-900)'}}>Smart Matching</h4>
              <p style={{color: 'var(--gray-600)', lineHeight: 1.6}}>AI-powered creator-brand matching based on audience, niche, and performance metrics.</p>
            </div>
            <div style={{textAlign: 'center', padding: '2rem'}}>
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>📊</div>
              <h4 style={{marginBottom: '1rem', color: 'var(--gray-900)'}}>Performance Tracking</h4>
              <p style={{color: 'var(--gray-600)', lineHeight: 1.6}}>Real-time analytics and insights for all your campaigns and collaborations.</p>
            </div>
            <div style={{textAlign: 'center', padding: '2rem'}}>
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>💰</div>
              <h4 style={{marginBottom: '1rem', color: 'var(--gray-900)'}}>Secure Payments</h4>
              <p style={{color: 'var(--gray-600)', lineHeight: 1.6}}>Built-in payment system with milestone-based releases and dispute resolution.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Ecosystem
