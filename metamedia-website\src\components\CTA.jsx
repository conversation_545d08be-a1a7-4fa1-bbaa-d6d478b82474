import React from 'react'
import { MessageCircle, Users, Calendar } from 'lucide-react'
import './Components.css'

const CTA = () => {
  return (
    <section className="cta-section">
      <div className="container">
        <div className="cta-content">
          <div className="cta-text">
            <h2>Whether you're a brand, creator or marketer</h2>
            <p>Let's build something that works.</p>
          </div>
          
          <div className="cta-buttons">
            <a href="#talk" className="btn btn-primary cta-btn">
              <MessageCircle size={20} />
              Talk to Us
            </a>
            <a href="#join" className="btn btn-secondary cta-btn">
              <Users size={20} />
              Join Now
            </a>
            <a href="#audit" className="btn btn-outline cta-btn">
              <Calendar size={20} />
              Book Free Audit
            </a>
          </div>
        </div>
      </div>


    </section>
  )
}

export default CTA
