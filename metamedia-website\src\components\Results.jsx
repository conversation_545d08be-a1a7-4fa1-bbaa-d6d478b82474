import React from 'react'
import { Star } from 'lucide-react'

const Results = () => {
  const companies = [
    { name: "TechCorp", logo: "🚀" },
    { name: "StartupX", logo: "💡" },
    { name: "<PERSON><PERSON>", logo: "🎯" },
    { name: "CompanyZ", logo: "⭐" },
    { name: "InnovateA", logo: "🔥" },
    { name: "GrowthB", logo: "📈" }
  ]

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Marketing Head at TechCorp",
      content: "MetaMedia transformed our social presence. Our engagement increased by 300% in just 3 months!",
      rating: 5,
      avatar: "👩‍💼"
    },
    {
      name: "<PERSON>",
      role: "Content Creator",
      content: "The ecosystem platform helped me connect with amazing brands and grow my audience significantly.",
      rating: 5,
      avatar: "👨‍🎨"
    },
    {
      name: "<PERSON>",
      role: "Startup Founder",
      content: "Their strategic approach to content creation and paid ads delivered results beyond our expectations.",
      rating: 5,
      avatar: "👩‍💻"
    }
  ]

  const stats = [
    { number: "500+", label: "Successful Campaigns" },
    { number: "200+", label: "Happy Clients" },
    { number: "1M+", label: "Content Views" },
    { number: "95%", label: "Client Retention" }
  ]

  return (
    <section className="section" id="results" style={{background: 'var(--gray-50)'}}>
      <div className="container">
        <div className="text-center" style={{marginBottom: '4rem'}}>
          <h2 style={{marginBottom: '1rem'}}>Results That Speak</h2>
          <p style={{fontSize: '1.125rem', color: 'var(--gray-600)'}}>Trusted by leading brands and creators worldwide</p>
        </div>

        {/* Company Logos */}
        <div style={{marginBottom: '4rem'}}>
          <h3 className="text-center" style={{marginBottom: '2rem', color: 'var(--gray-700)'}}>Companies We've Worked With</h3>
          <div className="grid" style={{gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '2rem', alignItems: 'center'}}>
            {companies.map((company, index) => (
              <div key={index} style={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '0.5rem', padding: '1rem', background: 'var(--white)', borderRadius: '0.5rem', boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)', transition: 'transform 0.3s ease'}}>
                <span style={{fontSize: '2rem'}}>{company.logo}</span>
                <span style={{fontWeight: 600, color: 'var(--gray-700)'}}>{company.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Stats */}
        <div style={{marginBottom: '4rem'}}>
          <div style={{background: 'var(--white)', borderRadius: '1rem', padding: '3rem 2rem', boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)', display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '2rem'}}>
            {stats.map((stat, index) => (
              <div key={index} style={{textAlign: 'center'}}>
                <div style={{fontSize: '3rem', fontWeight: 800, color: 'var(--primary-blue)', marginBottom: '0.5rem'}}>{stat.number}</div>
                <div style={{fontWeight: 600, color: 'var(--gray-600)'}}>{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div>
          <h3 className="text-center" style={{marginBottom: '3rem', color: 'var(--gray-900)'}}>What Our Clients Say</h3>
          <div className="grid grid-3">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="card" style={{background: 'var(--white)', position: 'relative'}}>
                <div style={{display: 'flex', gap: '0.25rem', marginBottom: '1rem', color: '#fbbf24'}}>
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} size={16} fill="currentColor" />
                  ))}
                </div>
                <p style={{fontStyle: 'italic', marginBottom: '1.5rem', color: 'var(--gray-700)', lineHeight: 1.6}}>"{testimonial.content}"</p>
                <div style={{display: 'flex', alignItems: 'center', gap: '1rem'}}>
                  <span style={{fontSize: '2rem'}}>{testimonial.avatar}</span>
                  <div>
                    <div style={{fontWeight: 600, color: 'var(--gray-900)'}}>{testimonial.name}</div>
                    <div style={{fontSize: '0.875rem', color: 'var(--gray-500)'}}>{testimonial.role}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default Results
