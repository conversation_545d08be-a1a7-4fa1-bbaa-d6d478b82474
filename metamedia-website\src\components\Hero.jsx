import React from 'react'
import { ArrowRight, Play } from 'lucide-react'
import './Components.css'

const Hero = () => {
  return (
    <section className="hero" id="home">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              More Than Marketing. <br />
              <span className="gradient-text">A Movement.</span>
            </h1>
            <p className="hero-subtitle">
              From content to conversions, join those digital tools, services, and collabs you need to grow.
            </p>
            <div className="hero-buttons">
              <a href="#services" className="btn btn-primary">
                Explore Services
                <ArrowRight size={20} />
              </a>
              <a href="#ecosystem" className="btn btn-secondary">
                Join the Ecosystem
              </a>
            </div>
          </div>
          
          <div className="hero-visual">
            <div className="laptop-container">
              <div className="laptop">
                <div className="laptop-screen">
                  <div className="screen-content">
                    <div className="floating-elements">
                      <div className="floating-icon icon-1">📱</div>
                      <div className="floating-icon icon-2">📊</div>
                      <div className="floating-icon icon-3">🎯</div>
                      <div className="floating-icon icon-4">💡</div>
                      <div className="floating-icon icon-5">🚀</div>
                    </div>
                    <div className="gradient-orb orb-1"></div>
                    <div className="gradient-orb orb-2"></div>
                    <div className="gradient-orb orb-3"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


    </section>
  )
}

export default Hero
