.header {
  background: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-circle {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: 50%;
  position: relative;
}

.logo-circle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: var(--white);
  border-radius: 50%;
}

.logo-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-blue);
}

.nav-desktop {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  font-weight: 500;
  color: var(--gray-600);
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--primary-blue);
}

.header-cta {
  display: flex;
  align-items: center;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--gray-600);
  cursor: pointer;
}

.nav-mobile {
  display: none;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
  border-top: 1px solid var(--gray-200);
}

.nav-link-mobile {
  padding: 0.5rem 0;
  font-weight: 500;
  color: var(--gray-600);
}

.mobile-cta {
  margin-top: 1rem;
  align-self: flex-start;
}

@media (max-width: 768px) {
  .nav-desktop,
  .header-cta {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .nav-mobile {
    display: flex;
  }
}
