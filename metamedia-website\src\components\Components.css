/* Hero Section */
.hero {
  background: var(--gradient-hero);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 80px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 80vh;
}

.hero-text {
  z-index: 2;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--white);
}

.gradient-text {
  background: linear-gradient(135deg, var(--gradient-cyan) 0%, var(--gradient-pink) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-visual {
  position: relative;
  z-index: 1;
}

.laptop-container {
  position: relative;
  perspective: 1000px;
}

.laptop {
  width: 500px;
  height: 300px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 20px;
  position: relative;
  transform: rotateY(-15deg) rotateX(10deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.laptop-screen {
  width: 90%;
  height: 75%;
  background: var(--gradient-primary);
  border-radius: 10px;
  position: absolute;
  top: 5%;
  left: 5%;
  overflow: hidden;
}

.screen-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-icon {
  position: absolute;
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
}

.icon-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.icon-2 {
  top: 60%;
  right: 20%;
  animation-delay: 0.5s;
}

.icon-3 {
  bottom: 30%;
  left: 30%;
  animation-delay: 1s;
}

.icon-4 {
  top: 40%;
  left: 60%;
  animation-delay: 1.5s;
}

.icon-5 {
  top: 10%;
  right: 30%;
  animation-delay: 2s;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(20px);
  animation: pulse 4s ease-in-out infinite;
}

.orb-1 {
  width: 100px;
  height: 100px;
  background: var(--gradient-pink);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 80px;
  height: 80px;
  background: var(--gradient-cyan);
  bottom: 20%;
  right: 15%;
  animation-delay: 1s;
}

.orb-3 {
  width: 60px;
  height: 60px;
  background: var(--accent-purple);
  top: 50%;
  left: 50%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* Services Section */
.services {
  background: var(--gray-50);
}

.services-header {
  margin-bottom: 4rem;
}

.services-header h2 {
  margin-bottom: 1rem;
}

.services-header p {
  font-size: 1.125rem;
  max-width: 600px;
  margin: 0 auto;
}

.services-grid {
  margin-bottom: 3rem;
}

.service-card {
  text-align: center;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.service-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  margin-bottom: 1.5rem;
  color: var(--white);
}

.service-card h3 {
  margin-bottom: 1rem;
  color: var(--gray-900);
}

.service-card p {
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-features li {
  padding: 0.5rem 0;
  color: var(--gray-600);
  position: relative;
  padding-left: 1.5rem;
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: bold;
}

.services-cta {
  margin-top: 2rem;
}

/* CTA Section */
.cta-section {
  background: var(--gradient-hero);
  padding: 5rem 0;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.cta-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.cta-text {
  margin-bottom: 3rem;
}

.cta-text h2 {
  font-size: 3rem;
  font-weight: 800;
  color: var(--white);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.cta-text p {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  min-width: 180px;
  transition: all 0.3s ease;
}

.btn-outline {
  background: transparent;
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--white);
  transform: translateY(-2px);
  color: var(--white);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .laptop {
    width: 300px;
    height: 180px;
  }

  .hero-buttons {
    justify-content: center;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .cta-text h2 {
    font-size: 2rem;
  }

  .cta-text p {
    font-size: 1.25rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 300px;
  }
}
