/* Meta Media Color Scheme & Base Styles */
:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Meta Media Color Scheme */
  --primary-blue: #4A90E2;
  --secondary-blue: #6BB6FF;
  --accent-purple: #8B5CF6;
  --gradient-purple: #A855F7;
  --gradient-pink: #EC4899;
  --gradient-cyan: #06B6D4;
  --dark-blue: #1E3A8A;
  --light-blue: #DBEAFE;
  --white: #FFFFFF;
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  --gradient-hero: linear-gradient(135deg, var(--primary-blue) 0%, var(--accent-purple) 50%, var(--gradient-pink) 100%);
  --gradient-card: linear-gradient(135deg, var(--white) 0%, var(--light-blue) 100%);
  --gradient-button: linear-gradient(135deg, var(--accent-purple) 0%, var(--gradient-purple) 100%);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--white);
  color: var(--gray-800);
  overflow-x: hidden;
}

html {
  scroll-behavior: smooth;
}

a {
  font-weight: 500;
  color: var(--primary-blue);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--accent-purple);
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--gray-900);
}

h1 {
  font-size: 3.5rem;
  line-height: 1.1;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 1.875rem;
}

p {
  line-height: 1.6;
  color: var(--gray-600);
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section {
  padding: 5rem 0;
}

.section-sm {
  padding: 3rem 0;
}

.text-center {
  text-align: center;
}

.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--gradient-button);
  color: var(--white);
  box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(139, 92, 246, 0.4);
  color: var(--white);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--white);
  transform: translateY(-2px);
  color: var(--white);
}

/* Cards */
.card {
  background: var(--white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
  background: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 50px;
  width: auto;
  transition: transform 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.nav-desktop {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-link {
  font-weight: 500;
  color: var(--gray-600);
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--primary-blue);
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--gray-600);
  cursor: pointer;
  flex-direction: column;
  gap: 4px;
}

.hamburger {
  width: 25px;
  height: 3px;
  background: var(--gray-600);
  transition: 0.3s;
}

.hamburger::before,
.hamburger::after {
  content: '';
  width: 25px;
  height: 3px;
  background: var(--gray-600);
  display: block;
  transition: 0.3s;
}

.hamburger::before {
  transform: translateY(-8px);
}

.hamburger::after {
  transform: translateY(5px);
}

.nav-mobile {
  display: none;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
  border-top: 1px solid var(--gray-200);
}

.nav-mobile.active {
  display: flex;
}

.nav-link-mobile {
  padding: 0.5rem 0;
  font-weight: 500;
  color: var(--gray-600);
}

.mobile-cta {
  margin-top: 1rem;
  align-self: flex-start;
}

/* Hero Section */
.hero {
  background: var(--gradient-hero);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 80px;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
  min-height: 80vh;
}

.hero-text {
  z-index: 2;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  color: var(--white);
}

.gradient-text {
  background: linear-gradient(135deg, var(--gradient-cyan) 0%, var(--gradient-pink) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-visual {
  position: relative;
  z-index: 1;
}

.laptop-container {
  position: relative;
  perspective: 1000px;
}

.laptop {
  width: 500px;
  height: 300px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 20px;
  position: relative;
  transform: rotateY(-15deg) rotateX(10deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.laptop-screen {
  width: 90%;
  height: 75%;
  background: var(--gradient-primary);
  border-radius: 10px;
  position: absolute;
  top: 5%;
  left: 5%;
  overflow: hidden;
}

.screen-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.floating-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

.floating-icon {
  position: absolute;
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
}

.icon-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.icon-2 {
  top: 60%;
  right: 20%;
  animation-delay: 0.5s;
}

.icon-3 {
  bottom: 30%;
  left: 30%;
  animation-delay: 1s;
}

.icon-4 {
  top: 40%;
  left: 60%;
  animation-delay: 1.5s;
}

.icon-5 {
  top: 10%;
  right: 30%;
  animation-delay: 2s;
}

.gradient-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(20px);
  animation: pulse 4s ease-in-out infinite;
}

.orb-1 {
  width: 100px;
  height: 100px;
  background: var(--gradient-pink);
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.orb-2 {
  width: 80px;
  height: 80px;
  background: var(--gradient-cyan);
  bottom: 20%;
  right: 15%;
  animation-delay: 1s;
}

.orb-3 {
  width: 60px;
  height: 60px;
  background: var(--accent-purple);
  top: 50%;
  left: 50%;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* Services Section */
.services {
  background: var(--gray-50);
}

.services-header {
  margin-bottom: 4rem;
}

.services-header h2 {
  margin-bottom: 1rem;
}

.services-header p {
  font-size: 1.125rem;
  max-width: 700px;
  margin: 0 auto;
}

.services-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.service-category {
  background: var(--white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.service-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.service-category:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.category-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.category-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--gradient-primary);
  border-radius: 50%;
  color: var(--white);
  flex-shrink: 0;
}

.category-header h3 {
  margin: 0;
  color: var(--gray-900);
  font-size: 1.5rem;
}

.service-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.service-list li {
  padding: 0.75rem 0;
  color: var(--gray-600);
  position: relative;
  padding-left: 1.5rem;
  border-bottom: 1px solid var(--gray-100);
  transition: color 0.3s ease;
}

.service-list li:last-child {
  border-bottom: none;
}

.service-list li::before {
  content: '▶';
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: bold;
  font-size: 0.8rem;
}

.service-list li:hover {
  color: var(--primary-blue);
  padding-left: 2rem;
}

.services-cta {
  margin-top: 3rem;
}

/* Ecosystem Section */
.ecosystem {
  background: var(--white);
}

.ecosystem-header {
  margin-bottom: 4rem;
}

.ecosystem-header h2 {
  margin-bottom: 1rem;
}

.ecosystem-subtitle {
  font-size: 1.25rem;
  max-width: 600px;
  margin: 0 auto;
  color: var(--gray-600);
}

.ecosystem-visual {
  margin-bottom: 4rem;
}

.ecosystem-flow {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.ecosystem-card {
  background: var(--white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 350px;
  text-align: center;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.creator-card {
  border-color: var(--accent-purple);
}

.brand-card {
  border-color: var(--primary-blue);
}

.ecosystem-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 50%;
  margin-bottom: 1rem;
  color: var(--white);
}

.ecosystem-card h3 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.ecosystem-card p {
  margin-bottom: 1.5rem;
  color: var(--gray-600);
}

.ecosystem-card ul {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
  text-align: left;
}

.ecosystem-card li {
  padding: 0.5rem 0;
  color: var(--gray-600);
  position: relative;
  padding-left: 1.5rem;
}

.ecosystem-card li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: var(--primary-blue);
  font-weight: bold;
}

.flow-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-blue);
  font-weight: 600;
  animation: rotate 3s linear infinite;
}

.ecosystem-features {
  margin-top: 4rem;
}

.feature-grid {
  gap: 2rem;
}

.feature-item {
  text-align: center;
  padding: 2rem;
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-item h4 {
  margin-bottom: 1rem;
  color: var(--gray-900);
}

.feature-item p {
  color: var(--gray-600);
  line-height: 1.6;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Results Section */
.results {
  background: var(--gray-50);
}

.results-header {
  margin-bottom: 4rem;
}

.results-header h2 {
  margin-bottom: 1rem;
}

.results-header p {
  font-size: 1.125rem;
  color: var(--gray-600);
}

.companies-section {
  margin-bottom: 4rem;
}

.companies-section h3 {
  margin-bottom: 2rem;
  color: var(--gray-700);
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  align-items: center;
}

.company-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--white);
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.company-logo:hover {
  transform: translateY(-2px);
}

.logo-emoji {
  font-size: 2rem;
}

.company-name {
  font-weight: 600;
  color: var(--gray-700);
}

.stats-section {
  margin-bottom: 4rem;
}

.stats-grid {
  background: var(--white);
  border-radius: 1rem;
  padding: 3rem 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  color: var(--primary-blue);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-weight: 600;
  color: var(--gray-600);
}

.testimonials-section h3 {
  margin-bottom: 3rem;
  color: var(--gray-900);
}

.testimonials-grid {
  gap: 2rem;
}

.testimonial-card {
  background: var(--white);
  position: relative;
}

.testimonial-rating {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 1rem;
  color: #fbbf24;
}

.star {
  font-size: 1rem;
}

.testimonial-content {
  font-style: italic;
  margin-bottom: 1.5rem;
  color: var(--gray-700);
  line-height: 1.6;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.author-avatar {
  font-size: 2rem;
}

.author-name {
  font-weight: 600;
  color: var(--gray-900);
}

.author-role {
  font-size: 0.875rem;
  color: var(--gray-500);
}

/* Blog Section */
.blog-teaser {
  background: var(--white);
}

.blog-header {
  margin-bottom: 4rem;
}

.blog-header h2 {
  margin-bottom: 1rem;
}

.blog-header p {
  font-size: 1.125rem;
  color: var(--gray-600);
}

.blog-content {
  margin-bottom: 3rem;
}

.blog-posts {
  margin-bottom: 4rem;
}

.blog-posts h3 {
  margin-bottom: 2rem;
  color: var(--gray-900);
}

.posts-grid {
  gap: 2rem;
}

.blog-card {
  transition: all 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.blog-category {
  background: var(--gradient-primary);
  color: var(--white);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.blog-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--gray-500);
  font-size: 0.875rem;
}

.blog-title {
  margin-bottom: 1rem;
  color: var(--gray-900);
  line-height: 1.4;
}

.blog-excerpt {
  margin-bottom: 1.5rem;
  color: var(--gray-600);
  line-height: 1.6;
}

.blog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-time {
  color: var(--gray-500);
  font-size: 0.875rem;
}

.read-more {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-blue);
  font-weight: 600;
  transition: color 0.3s ease;
}

.read-more:hover {
  color: var(--accent-purple);
}

.media-section h3 {
  margin-bottom: 2rem;
  color: var(--gray-900);
}

.media-grid {
  display: grid;
  gap: 1.5rem;
}

.media-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: var(--gray-50);
  border-radius: 1rem;
  transition: all 0.3s ease;
}

.media-item:hover {
  background: var(--white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.media-thumbnail {
  position: relative;
  width: 80px;
  height: 80px;
  background: var(--gradient-primary);
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
}

.media-type {
  position: absolute;
  bottom: -0.5rem;
  left: 50%;
  transform: translateX(-50%);
  background: var(--white);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.media-info h4 {
  margin-bottom: 0.5rem;
  color: var(--gray-900);
}

.media-info p {
  color: var(--gray-500);
  font-size: 0.875rem;
}

.blog-cta {
  margin-top: 2rem;
}

/* CTA Section */
.cta-section {
  background: var(--gradient-hero);
  padding: 5rem 0;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.cta-content {
  text-align: center;
  position: relative;
  z-index: 2;
}

.cta-text {
  margin-bottom: 3rem;
}

.cta-text h2 {
  font-size: 3rem;
  font-weight: 800;
  color: var(--white);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.cta-text p {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.cta-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-btn {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
  min-width: 180px;
  transition: all 0.3s ease;
}

.cta-section .btn-primary {
  background: var(--white);
  color: var(--primary-blue);
  box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3);
}

.cta-section .btn-primary:hover {
  background: var(--gray-100);
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(255, 255, 255, 0.4);
  color: var(--primary-blue);
}

.cta-section .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  border: 2px solid var(--white);
  backdrop-filter: blur(10px);
}

.cta-section .btn-secondary:hover {
  background: var(--white);
  color: var(--primary-blue);
  transform: translateY(-2px);
}

/* Footer */
.footer {
  background: var(--gray-900);
  color: var(--white);
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 4rem;
  margin-bottom: 3rem;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.footer-logo-image {
  height: 60px;
  width: auto;
  filter: brightness(0) invert(1);
  transition: transform 0.3s ease;
}

.footer-logo-image:hover {
  transform: scale(1.05);
}

.footer-tagline {
  color: var(--gray-300);
  line-height: 1.6;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--gray-800);
  border-radius: 50%;
  color: var(--gray-300);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

.link-group h4 {
  color: var(--white);
  margin-bottom: 1rem;
  font-weight: 600;
}

.link-group ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.link-group li {
  margin-bottom: 0.75rem;
}

.link-group a {
  color: var(--gray-300);
  transition: color 0.3s ease;
}

.link-group a:hover {
  color: var(--primary-blue);
}

.newsletter {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.newsletter h4 {
  color: var(--white);
  font-weight: 600;
}

.newsletter p {
  color: var(--gray-300);
  line-height: 1.6;
}

.newsletter-form {
  margin-top: 1rem;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: var(--gray-400);
  z-index: 1;
}

.input-group input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  background: var(--gray-800);
  border: 1px solid var(--gray-700);
  border-radius: 0.5rem;
  color: var(--white);
  font-size: 0.875rem;
}

.input-group input::placeholder {
  color: var(--gray-400);
}

.input-group input:focus {
  outline: none;
  border-color: var(--primary-blue);
}

.submit-btn {
  position: absolute;
  right: 0.5rem;
  background: var(--primary-blue);
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem;
  color: var(--white);
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover {
  background: var(--accent-purple);
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--gray-800);
  color: var(--gray-400);
  font-size: 0.875rem;
}

.footer-bottom-links {
  display: flex;
  gap: 2rem;
}

.footer-bottom-links a {
  color: var(--gray-400);
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: var(--primary-blue);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .laptop {
    width: 300px;
    height: 180px;
  }

  .hero-buttons {
    justify-content: center;
  }

  .services-categories,
  .grid-3 {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .category-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .ecosystem-flow {
    flex-direction: column;
    gap: 2rem;
  }

  .flow-arrow {
    transform: rotate(90deg);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .companies-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .posts-grid {
    grid-template-columns: 1fr;
  }

  .media-item {
    flex-direction: column;
    text-align: center;
  }

  .media-thumbnail {
    align-self: center;
  }

  .cta-text h2 {
    font-size: 2rem;
  }

  .cta-text p {
    font-size: 1.25rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-btn {
    width: 100%;
    max-width: 300px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .footer-links {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-bottom-links {
    gap: 1rem;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-purple);
}
